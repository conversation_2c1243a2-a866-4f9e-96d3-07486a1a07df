# Create c:\temp folder if it doesn't exist
if (-not (Test-Path -Path "c:\temp")) {
    New-Item -ItemType Directory -Path "c:\temp" -Force
    Write-Host "Created c:\temp folder"
} else {
    Write-Host "c:\temp folder already exists"
}

# Download the file to c:\temp
$downloadPath = "c:\temp\compressed.zip"
Write-Host "Downloading file to $downloadPath..."
Invoke-WebRequest -Uri '*******************************************************************%20(2).zip?sp=r&st=2025-08-14T16:19:28Z&se=2025-09-07T00:34:28Z&spr=https&sv=2024-11-04&sr=b&sig=8lL6Uk%2BUmE8rpy%2FoM69J44IKKepoKs7ey%2B03x69zz50%3D' -OutFile $downloadPath

# Extract the zip file in c:\temp
Write-Host "Extracting zip file..."
Expand-Archive -Path $downloadPath -DestinationPath "c:\temp" -Force
Write-Host "Extraction completed to c:\temp"

